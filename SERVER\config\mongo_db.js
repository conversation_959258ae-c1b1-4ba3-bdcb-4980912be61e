const mongoose = require('mongoose')

module.exports = async () => {
    try {
        const options = {
            useNewUrlParser: true,
            useUnifiedTopology: true
        };

        await mongoose.connect(process.env.MONGODB_URL, options)
        // console.log('Connected to MongoDB')

        // Test the connection with a simple query
        const db = mongoose.connection.db;
        await db.admin().ping();
        // console.log('MongoDB connection verified')
    } catch (e) {
        console.error('Error connecting to MongoDB:', e.message)
        process.exit(1)
    }
}