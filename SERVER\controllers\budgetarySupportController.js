const mongoose = require('mongoose');
const BudgetarySupport = require('../models/BudgetarySupport');

// Get all budgetary support entries
const getAllBudgetarySupport = async (req, res) => {
  try {
    const { fiscalYear, budgetType, region } = req.query;
    // console.log('Fetching budgetary support with params:', { fiscalYear, budgetType, region });

    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (region) query.region = region;

    const data = await BudgetarySupport.find(query);
    // console.log('Found data:', data);
    
    res.json(data);
  } catch (error) {
    console.error('Error in getAllBudgetarySupport:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get budgetary support by ID
const getBudgetarySupportById = async (req, res) => {
  try {
    const { id } = req.params;
    // console.log('Fetching budgetary support with ID:', id);

    const data = await BudgetarySupport.findById(id);
    if (!data) {
      return res.status(404).json({ message: 'Budgetary support entry not found' });
    }

    // console.log('Found data:', data);
    res.json(data);
  } catch (error) {
    console.error('Error in getBudgetarySupportById:', error);
    res.status(500).json({ message: error.message });
  }
};

// Create new budgetary support entry
const createBudgetarySupport = async (req, res) => {
  try {
    console.log('Creating budgetary support with data:', req.body);
    
    // Create entry data with required fields
    const entryData = { 
      ...req.body,
      processDate: new Date(),
      status: req.body.status || "Not Submitted"
    };
    
    // Only add createdBy if req.user exists and has a valid id
    if (req.user && req.user.id && mongoose.Types.ObjectId.isValid(req.user.id)) {
      entryData.createdBy = req.user.id;
    }
    
    const newEntry = new BudgetarySupport(entryData);
    const savedEntry = await newEntry.save();
    
    console.log('Created entry:', savedEntry);
    res.status(201).json(savedEntry);
  } catch (error) {
    console.error('Error in createBudgetarySupport:', error);
    res.status(400).json({ message: error.message });
  }
};

// Update budgetary support entry
const updateBudgetarySupport = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Updating budgetary support with ID:', id, 'Data:', req.body);
    
    // Create update data
    const updateData = { 
      ...req.body,
      processDate: req.body.processDate || new Date()
    };
    
    // Only add updatedBy if req.user exists and has a valid id
    if (req.user && req.user.id && mongoose.Types.ObjectId.isValid(req.user.id)) {
      updateData.updatedBy = req.user.id;
    }
    
    const updatedEntry = await BudgetarySupport.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );
    
    if (!updatedEntry) {
      return res.status(404).json({ message: 'Entry not found' });
    }
    
    console.log('Updated entry:', updatedEntry);
    res.json(updatedEntry);
  } catch (error) {
    console.error('Error in updateBudgetarySupport:', error);
    res.status(400).json({ message: error.message });
  }
};

// Delete budgetary support entry
const deleteBudgetarySupport = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Deleting budgetary support with ID:', id);
    
    const deletedEntry = await BudgetarySupport.findByIdAndDelete(id);
    
    if (!deletedEntry) {
      return res.status(404).json({ message: 'Entry not found' });
    }
    
    console.log('Deleted entry:', deletedEntry);
    res.json({ message: 'Entry deleted successfully' });
  } catch (error) {
    console.error('Error in deleteBudgetarySupport:', error);
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  getAllBudgetarySupport,
  getBudgetarySupportById,
  createBudgetarySupport,
  updateBudgetarySupport,
  deleteBudgetarySupport
};


